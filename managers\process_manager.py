"""
Universal Process Manager.
Contains universal functions used by storage, order, and product modules:
- pickup_process: Universal pickup process with payment support
- payment_process: Universal payment process with storno support  
- select_sections: Universal section selection process
"""

from managers.logger_manager import logger
import asyncio
from typing import List, Tuple, Any

from managers.ws_manager import ws_manager
from managers.session_manager import session_manager
from managers.sequence_manager import SequenceManager
from managers.session_manager import SectionConfig
from transaction.manager import transaction_manager


async def _start_section_sequence(session_id: str, section_ids: List[int], wait_for_completion: bool = False, start_if_active: bool = False) -> Tuple[bool, List[int]]:
    """
    Helper function to create SectionConfig objects and start FSM sequence.

    Args:
        session_id: WebSocket session ID
        section_ids: List of section IDs to open
        wait_for_completion: Whether to wait for sequence completion or return immediately

    Returns:
        Tuple of (success, successful_sections)
    """
    from managers.sequence_manager import SequenceManager
    from managers.session_manager import SectionConfig

    sequence_manager = SequenceManager()

    active_sequences = sequence_manager.get_active_sequences()
    if active_sequences and not start_if_active:
        logger.info(f"Active sequences found: {len(active_sequences)}, start_if_active: {start_if_active}")
        await ws_manager.send(session_id, {
            "type": "error",
            "message": f"There are already {len(active_sequences)} active sequences"
        })
        return False, []
    

    # Send acknowledgment that hardware screen ready was received
    await ws_manager.send(session_id, {
        "type": "hardware_status",
        "status": "preparing",
        "message": f"Připravuji schránky"
    })

    # Convert section IDs to SectionConfig objects
    section_configs = []
    for section_id in section_ids:
        section_configs.append(SectionConfig(
            section_id=section_id,
            lock_id=section_id,  # Default to same as section_id
            is_tempered=True,  # Default to tempered for pickup operations
            led_section=section_id  # Set LED section to same as section_id
        ))

    # Start FSM sequence
    sequence_started = await sequence_manager.start_fsm_sequence(
        session_id=session_id,
        sections=section_configs,
        pin=None,
        wait_for_close=wait_for_completion
    )

    successful_sections = []

    if sequence_started:
        if wait_for_completion:
            # Wait for sequence completion pattern
            logger.info(f"FSM sequence started successfully, waiting for completion")

            # Wait for the FSM sequence to complete by waiting for the task
            if session_id in sequence_manager.active_sequences:
                try:
                    # Wait for the sequence task to complete
                    await sequence_manager.active_sequences[session_id]
                    logger.info(f"FSM sequence completed successfully for {len(section_ids)} sections")
                    successful_sections.extend(section_ids)
                except Exception as e:
                    logger.error(f"FSM sequence failed with error: {e}")
                    return False, []
            else:
                logger.error("FSM sequence task not found in active sequences")
                return False, []
        else:
            # Immediate success pattern - add all sections to successful list
            successful_sections.extend(section_ids)
    else:
        logger.error("Failed to start FSM sequence")
        return False, []
    
    # await asyncio.sleep(60)


    return True, successful_sections


# Payment functions moved to managers/payment_manager.py
sequence_manager = SequenceManager()






async def pickup_process(sections: List[int], session_id: str, message_queue: asyncio.Queue) -> Tuple[bool, List[int]]:
    """
    Universal pickup process function used by storage, order, and product modules.

    This function handles ALL pickup-related WebSocket messages:
    - hardware_screen_ready: starts FSM sequence for all sections, then ends pickup process
    - open_sections: starts FSM sequence for multiple sections (expects list of integers)
    - hardware_screen_stop: ends pickup process

    The pickup process also ends when WebSocket connection is closed.

    Note: Payment handling has been moved to individual modules before calling this function.

    Args:
        sections: List of section IDs to pickup
        session_id: WebSocket session ID
        message_queue: Queue to receive WebSocket messages

    Returns:
        Tuple of (success, successful_sections)
    """
    logger.info(f"Starting pickup_process for {len(sections)} sections: {sections}")
    
    picking_up = True
    successful_sections = []

    # Register message queue for universal payment callback injection
    from transaction.manager import _active_message_queues
    _active_message_queues[session_id] = message_queue

    # Import sequence manager for FSM operations

    try:
        # Only send start_hardware_screen if we have sections to open
        await ws_manager.send(session_id, {
            "type": "start_hardware_screen",
            "wait_for_ready": True
        })
        
        # Main pickup loop - handle all message types
        while picking_up:
            # Check if WebSocket connection is still active
            if not ws_manager.is_connected(session_id):
                logger.info(f"WebSocket disconnected - ending pickup process for session {session_id}")
                # Clean up session when WebSocket disconnects
                await session_manager.remove_session(session_id)
                return True, successful_sections

            try:
                # Wait for message from websocket - no timeout
                message = await message_queue.get()
            except Exception as e:
                logger.info(f"WebSocket disconnected or error waiting for message in pickup_process: {e}")
                # End pickup process when WebSocket disconnects and clean up session
                logger.info(f"Ending pickup process due to WebSocket disconnection for session {session_id}")
                await session_manager.remove_session(session_id)
                return True, successful_sections

            message_type = message.get("type")
            logger.info(f"Processing pickup_process message: {message_type}")

            if message_type == "hardware_screen_ready":
                # Start FSM pickup sequence and open all sections automatically each after each
                logger.info(f"Hardware screen ready - starting FSM sequence for {len(sections)} sections")

                # Use helper function to start section sequence with completion waiting
                sequence_success, sequence_sections = await _start_section_sequence(
                    session_id=session_id,
                    section_ids=sections,
                    wait_for_completion=False
                )

                if sequence_success:
                    successful_sections.extend(sequence_sections)
                else:
                    logger.error("Failed to start FSM sequence for pickup process")


            elif message_type == "open_sections":
                section_ids = message.get("section_ids")

                # Validate that section_ids is a list of integers
                if not isinstance(section_ids, list):
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": "section_ids must be a list"
                    })
                    continue

                if not all(isinstance(sid, int) for sid in section_ids):
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": "All section_ids must be integers"
                    })
                    continue

                # Check if all section IDs are valid
                invalid_sections = [sid for sid in section_ids if sid not in sections]
                if invalid_sections:
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": f"Invalid sections: {invalid_sections}"
                    })
                    continue


                # Use helper function to start section sequence
                logger.info(f"Opening multiple sections: {section_ids}")

                sequence_success, sequence_sections = await _start_section_sequence(
                    session_id=session_id,
                    section_ids=section_ids,
                    wait_for_completion=False
                )

                if sequence_success:
                    successful_sections.extend(sequence_sections)
                else:
                    logger.error("Failed to start FSM sequence for pickup process")

            
            elif message_type == "hardware_screen_stop":
                # Stop pickup sequence or end pickup process
                logger.info(f"Received hardware_screen_stop command - ending pickup process for session {session_id}")
                picking_up = False

                await ws_manager.send(session_id, {
                    "type": "hardware_screen_completed",
                    "message": "Pickup process completed",
                    "successful_sections": successful_sections
                })

                # Delete websocket session and clean up
                logger.info(f"Cleaning up WebSocket session for {session_id}")
                ws_manager.disconnect(session_id)
                await session_manager.remove_session(session_id)

                return True, successful_sections

        return True, successful_sections
        
    except Exception as e:
        logger.error(f"Error in pickup_process: {e}")
        return False, []
    finally:
        # Clean up universal message queue registration
        from transaction.manager import _active_message_queues
        _active_message_queues.pop(session_id, None)


# Payment callback functions moved to managers/payment_manager.py


async def select_sections(
    reserved_sections: List[int],
    available_sections: List[int],
    session_id: str,
    message_queue: asyncio.Queue,
    package_pin: str=None,
    insert_pin: str=None
    ) -> Tuple[bool, List[int]]:
    """
    Universal section selection function used by order and other modules.
    Works exactly like pickup process() but for section selection.

    Args:
        reserved_sections: List of reserved section IDs (usually all available)
        available_sections: List of available section IDs to select from
        session_id: WebSocket session ID
        message_queue: Queue to receive WebSocket messages
        wait_for_stop: Whether to wait for stop_selection command (default: True)

    Returns:
        Tuple of (success, selected_sections)
    """
    logger.info(f"Starting section selection for session {session_id} with {len(available_sections)} available sections: {available_sections}")

    selecting = True
    opened_sections = []
    selected_sections = []
    print(f"Reserved sections: {reserved_sections}")

    # Register message queue for universal payment callback injection
    from transaction.manager import _active_message_queues
    _active_message_queues[session_id] = message_queue

    # Import sequence manager for FSM operations
    try:
        # No payment required, go directly to hardware screen
        await ws_manager.send(session_id, {
            "type": "start_hardware_screen",
            "wait_for_ready": True
        })
    
        # Main selection loop - handle all message types
        while selecting:
            # Check if WebSocket connection is still active
            if not ws_manager.is_connected(session_id):
                logger.info(f"WebSocket disconnected - ending section selection for session {session_id}")
                # Clean up session when WebSocket disconnects
                await session_manager.remove_session(session_id)
                return True, opened_sections

            try:
                # Wait for message from websocket - no timeout
                message = await message_queue.get()
            except Exception as e:
                logger.info(f"WebSocket disconnected or error waiting for message in select_sections: {e}")
                # End selection process when WebSocket disconnects and clean up session
                logger.info(f"Ending section selection due to WebSocket disconnection for session {session_id}")
                await session_manager.remove_session(session_id)
                return True, opened_sections

            message_type = message.get("type")
            logger.info(f"Processing pickup_process message: {message_type}")

            if message_type == "hardware_screen_ready":
                # Start FSM pickup sequence and open all sections automatically each after each
                logger.info(f"Hardware screen ready - starting FSM sequence for {len(reserved_sections)} sections")

                # Use helper function to start section sequence with completion waiting
                sequence_success, sequence_sections = await _start_section_sequence(
                    session_id=session_id,
                    section_ids=reserved_sections,
                    wait_for_completion=False
                )

                if sequence_success:
                    opened_sections.extend(sequence_sections)
                else:
                    logger.error("Failed to start FSM sequence for pickup process")

            elif message_type == "open_sections":
                section_ids = message.get("section_ids")

                # Validate that section_ids is a list of integers
                if not isinstance(section_ids, list):
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": "section_ids must be a list"
                    })
                    continue

                if not all(isinstance(sid, int) for sid in section_ids):
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": "All section_ids must be integers"
                    })
                    continue

                # Check if all section IDs are valid
                invalid_sections = [sid for sid in section_ids if sid not in available_sections]
                if invalid_sections:
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": f"Invalid sections: {invalid_sections}"
                    })
                    continue

                # Use helper function to start section sequence
                logger.info(f"Opening multiple sections: {section_ids}")

                sequence_success, sequence_sections = await _start_section_sequence(
                    session_id=session_id,
                    section_ids=section_ids
                )

                if sequence_success:
                    opened_sections.extend(sequence_sections)
                else:
                    logger.error("Failed to start FSM sequence for pickup process")

            elif message_type == "select_larger":       # only for order moduel
                # select automatically some larger section, if some prevous section was not opened, select some random valid section
                from infrastructure.repositories.order_repository import order_repository

                valid_section = order_repository.select_larger_section(previous_section_id=opened_sections[-1], package_pin=package_pin, insert_pin=insert_pin)

                if valid_section:
                    logger.info(f"Valid section found for select_larger - starting FSM sequence for section {valid_section}")
                    ws_manager.send(session_id, {
                        "type": "larger_found",
                        "section_id": valid_section,
                        "message": "Valid section found for select_larger"
                    })

                    # Use helper function to start section sequence with completion waiting
                    sequence_success, sequence_sections = await _start_section_sequence(
                        session_id=session_id,
                        section_ids=valid_section,
                        wait_for_completion=False
                    )

                    if sequence_success:
                        opened_sections.extend(sequence_sections)
                    else:
                        logger.error("Failed to start FSM sequence for pickup process")
                
                if not valid_section:
                    logger.warning("No valid section found for select_larger")
                    ws_manager.send(session_id, {
                        "type": "larger_not_found",
                        "message": "No valid section found for select_larger"
                    })

            elif message_type == "hardware_screen_stop":
                # Stop selection sequence or end selection process
                selected_sections = message.get("sections")
                logger.info(f"Received hardware_screen_stop command - ending selection process for session {session_id}")
                selecting = False

                await ws_manager.send(session_id, {
                    "type": "hardware_screen_completed",
                    "message": "Selection completed",
                    "selected_sections": selected_sections
                })

                # Delete websocket session and clean up
                logger.info(f"Cleaning up WebSocket session for {session_id}")
                ws_manager.disconnect(session_id)
                await session_manager.remove_session(session_id)
                return True, selected_sections

        return True, selected_sections

    except Exception as e:
        logger.error(f"Error in select_sections: {e}")
        return False, []
    finally:
        # Clean up universal message queue registration
        from transaction.manager import _active_message_queues
        _active_message_queues.pop(session_id, None)






async def age_check_process(session_id: str, message_queue: asyncio.Queue) -> bool:
    """
    Function to check customer's age at pickup_process

    Returns:
        bool: True if customer is over 18, False otherwise
    """
    return True

    await ws_manager.send(session_id, {
        "type": "start_age_check_screen",
        "wait_for_ready": True
    })
    # TODO: implement this funciton later, when i will now how it will wokrk
    await asyncio.sleep(2)  # Simulate age check delay
    success = True

    if not success:
        logger.info(f"Age check failed for session {session_id}")
        await ws_manager.send(session_id, {
            "type": "age_check_result",
            "success": success
        })
        return False

    await ws_manager.send(session_id, {
        "type": "age_check_result",
        "success": success
    })
    return True
