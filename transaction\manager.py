from managers.logger_manager import logger
import aiohttp
import asyncio
import httpx
import mysql.connector
import json
from datetime import datetime
from typing import Dict, Optional, Any
from os import getenv
from dotenv import load_dotenv
from uuid import uuid4

from config import device_config
from managers import ws_manager
from managers.timeline_logger import log_timeline_event

load_dotenv()

# Global registry for active message queues (for universal payment callback injection)
_active_message_queues: Dict[str, asyncio.Queue] = {}

class TransactionManager:
    """Transaction manager with payment processing and close_totals functionality"""

    def __init__(self):
        self.external_services = {
            "payment": "http://localhost:8080/",
        }

    def _get_db_connection(self):
        """Get database connection"""
        return mysql.connector.connect(
            host=getenv("DB_HOST"),
            port=int(getenv("DB_PORT")),
            database=getenv("DB_NAME"),
            user=getenv("DB_USER"),
            password=getenv("DB_PASSWORD")
        )

    # Removed register_message_queue() - use direct _active_message_queues[session_id] = message_queue

    async def inject_payment_callback(self, session_id: str, status: str, message: str = "") -> bool:
        """
        Universal payment callback injection function.
        Injects payment status directly into the registered message queue for any module.

        Args:
            session_id: Session ID to inject callback for
            status: Payment status ("success" or "failed")
            message: Optional message from payment service

        Returns:
            bool: True if injection successful, False otherwise
        """
        logger.info(f"Universal payment callback injection for session {session_id}: {status}")

        try:
            # Check if we have an active message queue for this session
            if session_id not in _active_message_queues:
                logger.error(f"No active message queue found for payment callback: {session_id}")
                return False

            message_queue = _active_message_queues[session_id]

            # Create payment status message
            success = status == "success"
            callback_message = {
                "type": "payment_status",
                "success": success,
                "message": message or ("Payment successful" if success else "Payment failed")
            }

            # Inject the callback message into the queue
            await message_queue.put(callback_message)
            logger.info(f"Payment callback injected successfully for session {session_id}")

            return True

        except Exception as e:
            logger.error(f"Error injecting payment callback for session {session_id}: {e}")
            return False

    async def close_totals(self) -> bool:
        """
        Close totals operation - sends close_totals request to payment terminal.
        Called from echo_loop() in jetveo_client.py.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info("Starting close_totals operation")

            # Prepare request data
            data = {
                'type': 'close_totals'
            }

            logger.info(f"Sending close_totals request: {data}")

            async with aiohttp.ClientSession() as session:
                try:
                    async with session.post(
                        self.external_services["payment"],
                        json=data,
                        headers={'Content-Type': 'application/json'},
                        timeout=30
                    ) as response:
                        response_text = await response.text()
                        logger.info(f"Close totals response: {response.status} - {response_text}")

                        if response.status == 200:
                            try:
                                result = await response.json()
                                if result.get('status') == 'success':
                                    msg = "Close totals operation completed successfully"
                                    success = True
                                else:
                                    msg = f"Close totals failed: {result.get('message', 'Unknown error')}"
                                    success = True  # Temporary, because terminal returns success false
                            except Exception as json_error:
                                logger.warning(f"Could not parse close_totals response as JSON: {json_error}")
                                msg = "Close totals completed (response not JSON)"
                                success = True
                        else:
                            msg = f"Close totals HTTP error: {response.status} - {response_text}"
                            success = True  # Temporary, because terminal returns success false

                except aiohttp.ClientTimeout:
                    msg = "Close totals timeout"
                    logger.error(msg)
                    success = False

                except aiohttp.ClientError as e:
                    msg = f"Connection error during close_totals: {str(e)}"
                    logger.error(msg)
                    success = False

        except Exception as e:
            msg = f"Unexpected error in close_totals: {str(e)}"
            logger.error(msg)
            success = False

        if success:
            logger.info("Close totals operation completed successfully")

        log_timeline_event(
            event_type="close_totals",
            event_result="success" if success else "failed",
            message=msg
        )
        return success


    async def payment_process_simple(self, amount: float, mode: str) -> bool:
        """
        Universal payment process function.
        Simple function that processes payment and returns success/failure.

        Args:
            amount: Payment amount in currency units
            mode: Payment mode ("sale" or "storage")

        Returns:
            bool: True if payment successful, False if failed
        """
        logger.info(f"Starting universal payment process for amount: {amount}")

        if amount <= 0:
            logger.info("No payment needed (amount <= 0)")
            return True

        # Prepare payment data (same format as existing code)
        payment_data = {
            "type": "sale",
            "amount": float(amount),
            "variable_symbol": f"payment_{int(datetime.now().timestamp())}"  # Unique identifier
        }

        logger.info(f"Processing payment: {payment_data}")

        # Log initial payment request
        await self.log_payment(
            event_type="new_payment",
            mode=mode,
            request=payment_data,
            response={},
            result="payment_process_started",
            message="Payment request sent to terminal"
        )

        try:
            # Get payment service configuration
            payment_service_timeout = device_config.payment_config.get("payment_service_timeout", 30)
            payment_service_url = device_config.payment_config.get("payment_service_url")

            if not payment_service_url:
                logger.error("Payment service URL not configured")
                # Log configuration error
                await self.log_payment(
                    mode=mode,
                    request=payment_data,
                    response={"error": "Payment service URL not configured"},
                    result="failed",
                    message="Payment service URL not configured"
                )
                return False

            # Send payment request to payment service
            async with httpx.AsyncClient(timeout=payment_service_timeout) as client:
                response = await client.post(
                    payment_service_url,
                    json=payment_data
                )

                response_data = {
                    "status_code": response.status_code,
                    "response_text": response.text
                }


                if response.status_code == 200:
                    logger.info(f"Payment successful: {response.status_code} - {response.text}")
                    # Log successful payment
                    await self.log_payment(
                        mode=mode,
                        request=payment_data,
                        response=response_data,
                        result="success",
                        message="Payment completed successfully"
                    )
                    return True
                else:
                    logger.error(f"Payment failed: {response.status_code} - {response.text}")
                    # Log failed payment
                    await self.log_payment(
                        mode=mode,
                        request=payment_data,
                        response=response_data,
                        result="failed",
                        message=f"Payment failed with status {response.status_code}"
                    )
                    return False

        except httpx.TimeoutException:
            logger.error(f"Payment service timeout for amount {amount}")
            # Log timeout error
            await self.log_payment(
                event_type="payment_timeout",
                mode=mode,
                request=payment_data,
                response={"error": "timeout"},
                result="failed",
                message="Payment service timeout"
            )
            return False

        except Exception as e:
            logger.error(f"Payment error: {e}")
            # Log general error
            await self.log_payment(
                event_type="payment_error",
                mode=mode,
                request=payment_data,
                response={"error": str(e)},
                result="failed",
                message=f"Payment error: {str(e)}"
            )
            return False

    async def make_payment(self, session_id: str, amount: float, message_queue: asyncio.Queue, mode: str, pin_entered: str = None, section_id: str = None) -> bool:
        """
        Full payment process with WebSocket messages and callback handling.
        Used when payment needs to be integrated with WebSocket flow.

        Args:
            session_id: WebSocket session ID
            amount: Payment amount in currency units
            message_queue: Queue to receive WebSocket messages
            mode: Payment mode ("sale" or "storage")
            pin_entered: Optional PIN entered by customer (for timeline logging)
            section_id: Optional section ID (for timeline logging)

        Returns:
            bool: True if payment successful, False if failed
        """
        logger.info("Starting payment process")

        payment_result = False
        msg = None

        # Register message queue for universal payment callback injection
        _active_message_queues[session_id] = message_queue

        try:
            # Send start payment screen message
            await ws_manager.send(session_id, {
                "type": "start_payment_screen",
                "wait_for_ready": True
            })

            # Wait for payment_screen_ready
            while True:
                try:
                    message = await message_queue.get()
                except Exception as e:
                    logger.error(f"Error waiting for payment message: {e}")
                    return False

                message_type = message.get("type")
                logger.info(f"Processing payment message: {message_type}")

                if message_type == "payment_screen_ready":
                    logger.info("Payment screen ready - starting payment terminal call")

                    # Send payment initiation status
                    await ws_manager.send(session_id, {
                        "type": "payment_status",
                        "status": "initiating"
                    })

                    # Call payment terminal (this initiates payment but doesn't wait for result)
                    payment_initiated = await self.payment_process_simple(amount, mode)

                    if payment_initiated:
                        # Send payment processing status
                        await ws_manager.send(session_id, {
                            "type": "payment_status",
                            "status": "processing"
                        })
                        logger.info("Payment initiated successfully, waiting for callback")
                        # Continue waiting for payment_status callback - don't break here
                    else:
                        # Payment terminal call failed
                        await ws_manager.send(session_id, {
                            "type": "payment_result",
                            "success": False,
                            "message": "Payment failed to initiate"
                        })
                        payment_result = False
                        break  # Exit the while loop after payment initiation failure

                elif message_type == "payment_status":
                    # Handle payment status from payment callback
                    logger.info("Received payment status callback")

                    success = message.get("success", False)
                    msg = message.get("message", None)

                    # Send payment result
                    await ws_manager.send(session_id, {
                        "type": "payment_result",
                        "success": success,
                        "message": "Payment successful" if success else "Payment failed"
                    })

                    if success:
                        logger.info("Payment completed successfully")
                        payment_result = True
                        break  # Exit the while loop after successful payment
                    else:
                        logger.info("Payment failed")
                        payment_result = False
                        break  # Exit the while loop after failed payment

        except Exception as e:
            logger.error(f"Error in payment_process: {e}")
            payment_result = False
        finally:
            # Clean up universal message queue registration
            _active_message_queues.pop(session_id, None)

            # log_timeline_event(
            #     event_type="make_payment",
            #     event_result="success" if payment_result else "failed",
            #     message=f"Payment completed with message: {msg}",
            #     mode=mode,
            #     entered_pin=pin_entered,
            #     section_id=section_id
            # )
            return payment_result

    async def log_payment(
        self,
        event_type: str,
        mode: str,
        request: Dict[str, Any],
        response: Dict[str, Any],
        result: str,
        message: str = None
        ) -> None:
        """
        Log payment transaction to database.

        Args:
            mode: Payment mode ("sale" or "storage")
            request: Payment request data
            response: Payment response data
            result: Transaction result ("success", "failed", etc.)
            message: Optional message/description
        """
        try:

            # first log journal
            await log_timeline_event(
                event_type=event_type,
                event_result=result,
                message=message,
                mode=mode
            )

            conn = self._get_db_connection()
            cursor = conn.cursor()

            # Generate UUID for the transaction
            transaction_uuid = str(uuid4())

            # Determine which table to use and prepare the query
            if mode == "sale":
                query = """
                    INSERT INTO sale_transactions (
                        uuid, type, msg, result, request, response
                    ) VALUES (%s, %s, %s, %s, %s, %s)
                """
            elif mode == "storage":
                query = """
                    INSERT INTO storage_transactions (
                        uuid, type, msg, result, request, response
                    ) VALUES (%s, %s, %s, %s, %s, %s)
                """
            else:
                logger.error(f"Unsupported mode: {mode}")
                return

            # Prepare values
            values = (
                transaction_uuid,
                mode,
                message,
                result,
                json.dumps(request) if request else None,
                json.dumps(response) if response else None
            )

            # Execute the query
            cursor.execute(query, values)
            conn.commit()

            logger.info(f"Payment transaction logged to {mode}: {transaction_uuid} - {result}")

        except mysql.connector.Error as err:
            if 'conn' in locals():
                conn.rollback()
            logger.error(f"Database error logging payment transaction to {mode}: {err}")
        except Exception as e:
            if 'conn' in locals():
                conn.rollback()
            logger.error(f"Unexpected error logging payment transaction to {mode}: {e}")
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()

# Global instance
transaction_manager = TransactionManager()
