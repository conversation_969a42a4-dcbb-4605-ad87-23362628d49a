"""
WebSocket handler for storage operations.
Handles storage pickup using pickup_process directly and storage insertion using select_sections from process_manager.
"""

import json
import asyncio
from fastapi import WebSocket, WebSocketDisconnect
from managers import ws_manager, session_manager
from infrastructure.repositories.storage_repository import StorageRepository
from managers.logger_manager import logger


async def handle_storage_pickup_websocket(websocket: WebSocket, session_id: str):
    """
    Handle WebSocket connection for storage pickup operations.
    Uses pickup_process directly as defined in screen_communication.md

    Args:
        websocket: FastAPI WebSocket connection
        session_id: Session ID for this connection
    """
    logger.info(f"Storage pickup WebSocket handler started for session: {session_id}")

    try:
        # Register WebSocket connection
        await ws_manager.connect(session_id, websocket)
        logger.info(f"Storage pickup WebSocket connected: {session_id}")

        # Get session data
        session = session_manager.get_session(session_id)
        if not session:
            logger.error(f"No session found for {session_id}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "Session not found"
            })
            return

        section_id = getattr(session, 'section_id', None)
        requires_payment = getattr(session, 'amount', 0) > 0
        payment_completed = getattr(session, 'payment_completed', False)

        if not section_id:
            logger.error(f"No section_id in session {session_id}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "No section ID found"
            })
            return

        # Create message queue for pickup_process
        message_queue = asyncio.Queue()

        # Message handler to route WebSocket messages to pickup_process
        async def handle_websocket_messages():
            while ws_manager.is_connected(session_id):
                try:
                    message = await websocket.receive_text()
                    if not message or message.strip() == "":
                        continue

                    try:
                        data = json.loads(message)
                    except json.JSONDecodeError:
                        await ws_manager.send(session_id, {"type": "error", "message": "Invalid JSON"})
                        continue

                    msg_type = data.get("type")

                    if msg_type == "ping":
                        await ws_manager.send(session_id, {"type": "pong"})
                        continue

                    # Route message to pickup_process
                    await message_queue.put(data)

                except WebSocketDisconnect:
                    logger.info(f"Storage pickup WebSocket disconnected: {session_id}")
                    break
                except Exception as e:
                    logger.error(f"Error processing WebSocket message: {e}")
                    break

        # Start message handler task
        message_task = asyncio.create_task(handle_websocket_messages())

        # Handle payment if required before calling pickup_process
        if requires_payment and not payment_completed:
            # Show info screen first
            await show_info_screen(session_id, message_queue)

            logger.info("Payment required, processing payment before pickup")
            session_manager.update_session(session_id, current_step="payment")

            # Get amount from session
            amount = getattr(session, 'amount', 0)
            reservation_id = getattr(session, 'reservation_id', None)

            from infrastructure.repositories.storage_repository import storage_repository
            from transaction.manager import transaction_manager
            payment_success = await transaction_manager.make_payment(
                session_id=session_id,
                amount=amount,
                message_queue=message_queue,
                mode="storage",
                pin_entered=getattr(session, 'log_pin', None),
                section_id=section_id
            )

            if not payment_success:
                logger.info("Payment failed or cancelled - ending pickup process")
                message_task.cancel()
                return
            
            # Persist payment info on the active reservation for the section
            await storage_repository.edit_reservations(
                section_id=section_id,
                paid_status=1,
                action=3,
                paid_fee=amount
            )

            # Mark payment as completed
            session_manager.update_session(session_id, payment_completed=True, current_step="hardware")
            logger.info("Payment completed successfully - proceeding to pickup")
        elif not requires_payment or payment_completed:
            # Show info screen even when no payment is required
            await show_info_screen(session_id, message_queue)
            session_manager.update_session(session_id, current_step="hardware")

        # Start pickup_process from universal process_manager (payment already handled)
        from managers.process_manager import pickup_process
        success, successful_sections = await pickup_process(
            sections=[section_id],
            session_id=session_id,
            message_queue=message_queue
        )

        # Mark as completed if successful
        if success:
            session_manager.update_session(session_id, current_step="completed", payment_completed=True)

        # Cancel message handler
        message_task.cancel()

        logger.info(f"Storage pickup completed for session {session_id}: success={success}")

    except Exception as e:
        logger.error(f"Error in storage pickup WebSocket handler: {e}")
    finally:
        # Clean up
        ws_manager.disconnect(session_id)
        logger.info(f"Storage pickup WebSocket handler ended for session: {session_id}")


async def handle_storage_insert_websocket(websocket: WebSocket, session_id: str):
    """
    Handle WebSocket connection for storage insertion operations.
    Uses select_sections function from process_manager for door opening.

    Args:
        websocket: FastAPI WebSocket connection
        session_id: Session ID for this connection
    """
    logger.info(f"Storage insert WebSocket handler started for session: {session_id}")

    try:
        # Register WebSocket connection
        await ws_manager.connect(session_id, websocket)
        logger.info(f"Storage insert WebSocket connected: {session_id}")

        # Get session data
        session = session_manager.get_session(session_id)
        if not session:
            logger.error(f"No session found for {session_id}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "Session not found"
            })
            return

        section_id = getattr(session, 'section_id', None)
        if not section_id:
            logger.error(f"No section_id in session {session_id}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "No section ID found"
            })
            return

        # Create message queue for communication with select_sections
        message_queue = asyncio.Queue()


        # Message handler to route WebSocket messages to select_sections
        async def handle_websocket_messages():
            while ws_manager.is_connected(session_id):
                try:
                    message = await websocket.receive_text()
                    if not message or message.strip() == "":
                        continue

                    try:
                        data = json.loads(message)
                    except json.JSONDecodeError:
                        await ws_manager.send(session_id, {"type": "error", "message": "Invalid JSON"})
                        continue

                    msg_type = data.get("type")

                    if msg_type == "ping":
                        await ws_manager.send(session_id, {"type": "pong"})
                        continue

                    # Route message to select_sections
                    await message_queue.put(data)

                except WebSocketDisconnect:
                    logger.info(f"Storage insert WebSocket disconnected: {session_id}")
                    break
                except Exception as e:
                    logger.error(f"Error processing WebSocket message: {e}")
                    break

        # Start message handler task
        message_task = asyncio.create_task(handle_websocket_messages())

        # Get session data for payment requirements
        
        requires_payment = getattr(session, 'amount', 0) > 0
        current_step = getattr(session, 'current_step', None)
        payment_completed = getattr(session, 'payment_completed', False)

        # PAYMENT STEP - Use universal pickup_process for payment handling
        if requires_payment and not payment_completed:

            # show info screen
            await show_info_screen(session_id, message_queue)


            logger.info("Payment required, using universal pickup_process for payment")
            # Update session state
            session_manager.update_session(session_id, current_step="payment")

            # Use universal pickup_process just for payment (with empty sections list)
            from transaction.manager import transaction_manager
            payment_success = await transaction_manager.make_payment(
                session_id=session_id,
                amount=getattr(session, 'amount', 0),
                message_queue=message_queue,
                mode="storage",
                pin_entered=getattr(session, 'log_pin', None),
                section_id=section_id
            )

            if not payment_success:
                logger.info("Payment failed or cancelled - ending insertion process")
                return

            # Mark payment as completed
            session_manager.update_session(session_id, payment_completed=True, current_step="hardware")
            logger.info("Payment completed successfully - proceeding to section selection")
        elif requires_payment and payment_completed:
            logger.info("Payment already completed, skipping payment step")
            session_manager.update_session(session_id, current_step="hardware")

        # CREATE RESERVATION
        from infrastructure.repositories.storage_repository import storage_repository as repo
        reservation_uuid, pickup_pin = repo.create_reservation(
            section_id=section_id,
            price=getattr(session, 'amount', 0),
            size_category=getattr(session, 'size_category', 0),
            email=getattr(session, 'email', None)
            )
        
        logger.info(f"Created storage reservation {reservation_uuid} for section {section_id} with pickup pin {pickup_pin}")

        # Calculate expiration time in format 22.09.2025 10:00:00
        # expiration_time = current_time + MAX_STORAGE_HOURS - 1
        from datetime import datetime, timedelta
        from os import getenv

        max_storage_hours = int(getenv("MAX_STORAGE_HOURS", "25"))
        current_time = datetime.now()
        expiration_time_dt = current_time + timedelta(hours=max_storage_hours)
        expiration_time = expiration_time_dt.strftime("%d.%m.%Y %H:%M")

        await ws_manager.send(session_id, {
            "type": "start_pin_screen",
            "pickup_pin": pickup_pin,
            "message": "reservation created, waiting for pin confirmation",
            "expiration_time": expiration_time,
            "expiration_time_hours": max_storage_hours
        })

        # PIN CONFIRMATION
        pin_confirmed_state = 0
        while pin_confirmed_state != 2:
            try:
                message = await message_queue.get()
                if message.get("type") == "pin_screen_ready":
                    await ws_manager.send(session_id, {
                        "type": "stop_pin_screen",
                        "pickup_pin": pickup_pin,
                        "message": "waiting for pin_screen_stop command",
                        "expiration_time": expiration_time,
                        "expiration_time_hours": max_storage_hours
                    })
                    pin_confirmed_state = 1
                
                if message.get("type") == "pin_screen_stop" and pin_confirmed_state == 1:
                    pin_confirmed_state = 2
            except Exception as e:
                logger.error(f"Error waiting for pin confirmation: {e}")
                break

        # OPEN SECTION
        from managers.process_manager import select_sections
        success, selected_sections = await select_sections(
            reserved_sections=[section_id],  # Only one section is reserved for storage insertion
            available_sections=[section_id],  # Only one section is available for storage insertion
            session_id=session_id,
            message_queue=message_queue,
        )

        # Cancel message handler
        message_task.cancel()

        # Complete storage insertion operation
        if success:
            session_manager.update_session(session_id, current_step="completed")
            logger.info(f"Storage insertion completed successfully for section {section_id}")

        logger.info(f"Storage insertion completed for session {session_id}: success={success}")

    except Exception as e:
        logger.error(f"Error in storage insert WebSocket handler: {e}")
    finally:
        # Clean up WebSocket connection
        ws_manager.disconnect(session_id)
        logger.info(f"Storage insert WebSocket handler ended for session: {session_id}")




async def handle_storage_websocket(websocket: WebSocket, session_id: str):
    """
    Main WebSocket handler for storage operations.
    Routes to appropriate handler based on session operation type.
    """
    from managers.session_manager import session_manager

    session = session_manager.get_session(session_id)
    if not session:
        logger.error(f"No session found for {session_id}")
        await websocket.close(code=1000, reason="Session not found")
        return

    operation = getattr(session, 'operation', None)

    if operation == "storage_pickup":
        await handle_storage_pickup_websocket(websocket, session_id)
    elif operation == "storage_insert":
        await handle_storage_insert_websocket(websocket, session_id)
    else:
        # No other storage operations supported
        logger.error(f"Unknown storage operation: {operation}")
        await websocket.close(code=1000, reason="Unknown operation")



async def show_info_screen(session_id: str, message_queue: asyncio.Queue):
    """
    Show info screen with storage pickup information based on timing rules.
    Function ends after receiving message with type "stop_info_screen".
    """
    from managers import session_manager, ws_manager
    from datetime import datetime, timedelta
    from os import getenv
    import math

    session = session_manager.get_session(session_id)
    if not session:
        logger.error(f"No session found for {session_id}")
        return

    # Get reservation info from session
    reservation_pin = getattr(session, 'pin', None)
    if not reservation_pin:
        logger.error(f"No reservation PIN found in session {session_id}")
        return

    # Find reservation to get creation time and other details
    from infrastructure.repositories.storage_repository import storage_repository
    reservations = storage_repository.find_reservations(reservation_pin=reservation_pin, status=1, limit=1)
    if not reservations:
        logger.error(f"No active reservation found for PIN {reservation_pin}")
        return

    reservation = reservations[0]
    created_at = reservation['created_at']
    current_time = datetime.now()

    # Handle datetime format
    if isinstance(created_at, str):
        created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00'))

    time_diff = current_time - created_at
    hours_passed = time_diff.total_seconds() / 3600

    # Get configuration
    cancel_time_minutes = int(getenv("STORAGE_RESERVATION_CANCEL_TIME", "15"))
    max_storage_hours = getattr(session, 'max_storage_hours', reservation.get('max_hours', 25))
    storage_expiration_time_str = getattr(session, 'storage_expiration_time_str', None)
    # price_per_cycle = float(reservation.get('price', 0) or 0)
    extra_charge_amount = getattr(session, 'amount', 0)

    # Calculate expiration time if not in session
    if not storage_expiration_time_str:
        expiration_time_dt = created_at + timedelta(hours=max_storage_hours)
        storage_expiration_time_str = expiration_time_dt.strftime("%d.%m.%Y %H:%M:%S")

    # Calculate valid time expiration (15 minutes from creation)
    valid_time_expiration = created_at + timedelta(minutes=cancel_time_minutes)
    minutes_to_valid_expiration = max(0, (valid_time_expiration - current_time).total_seconds() / 60)

    # Check if this is a re-pickup scenario (deactivated reservation)
    is_re_pickup = reservation.get('status') == 0

    # Prepare calculated data message
    data_message = {"type": "start_info_screen"}

    if is_re_pickup:  # Re-pickup within allowed time
        data_message.update({
            "info_type": "re_pickup",
            "expiration_time_str": storage_expiration_time_str,
            "expiration_time_datetime": (created_at + timedelta(hours=max_storage_hours)).isoformat(),
            "expiration_time_hours": max_storage_hours
        })
    elif hours_passed < (cancel_time_minutes / 60):  # Within cancel time (15 minutes)
        data_message.update({
            "info_type": "in_valid_insert_time",
            "expiration_time_str": storage_expiration_time_str,
            "expiration_time_datetime": (created_at + timedelta(hours=max_storage_hours)).isoformat(),
            "valid_time_expiration": valid_time_expiration.isoformat(),
            "minutes_to_valid_time_expiration": round(minutes_to_valid_expiration, 1)
        })
    elif hours_passed <= max_storage_hours:  # After cancel time but within max hours
        data_message.update({
            "info_type": "normal",
            "expiration_time_str": storage_expiration_time_str,
            "expiration_time_datetime": (created_at + timedelta(hours=max_storage_hours)).isoformat(),
            "expiration_time_hours": max_storage_hours
        })
    else:  # Exceeded max storage hours
        extra_cycles = math.ceil((hours_passed - max_storage_hours) / max_storage_hours)

        data_message.update({
            "info_type": "exceeded_max_hours",
            "max_storage_hours": max_storage_hours,
            "extra_charge_amount": extra_charge_amount,
            "extra_cycles": extra_cycles,
            "expiration_time_str": storage_expiration_time_str,
            "expiration_time_datetime": (created_at + timedelta(hours=max_storage_hours)).isoformat(),
            "expiration_time_hours": max_storage_hours
        })

    # Send start_info_screen message with all calculated data
    await ws_manager.send(session_id, data_message)
    logger.info(f"Sent start_info_screen with data for session {session_id}: {data_message['info_type']}")

    # Wait for info_screen_ready message and send data again
    while True:
        message = await message_queue.get()
        if message.get("type") == "info_screen_ready":
            logger.info(f"Received info_screen_ready for session {session_id}")

            # Change type from "start_info_screen" to "stop_info_screen"
            data_message["type"] = "stop_info_screen"

            # Send the calculated data again after receiving info_screen_ready
            await ws_manager.send(session_id, data_message)
            logger.info(f"Sent info screen data again for session {session_id}: {data_message['info_type']}")
            break

    # Wait for info_screen_stop message
    try:
        while True:
            message = await message_queue.get()

            if message.get("type") == "info_screen_stop":
                # Handle cancel_reservation flag for in_valid_insert_time case
                if data_message["info_type"] == "in_valid_insert_time":
                    cancel_reservation = message.get("cancel_reservation", False)
                    session_manager.update_session(session_id, storage_deactivate_reservation=cancel_reservation)
                    logger.info(f"Updated session {session_id} storage_deactivate_reservation to {cancel_reservation}")

                logger.info(f"Info screen stopped for session {session_id}")
            
                if message.get("cancel", False) == True:
                    await ws_manager.send(session_id, {"type": "pickup_canceled", "message": "Pickup canceled"})
                    logger.info(f"Pickup canceled by user for session {session_id}")

                    # Close WebSocket connection and clean up session
                    ws_manager.disconnect(session_id)
                    await session_manager.remove_session(session_id)

                    # End the info screen flow
                    return

                break

    except Exception as e:
        logger.error(f"Error in show_info_screen for session {session_id}: {e}")



"""
funkcni
{
  "SerialNumber": "123456789-1",
  "ReservationUuid": "1997a8b1-83cb-4808-9485-cf3b8ec3f40d",
  "Timestamp": "2025-09-16T14:00:29.643138Z",
  "ReservationPin": "965341",
  "SectionId": 3,
  "Email": "asfd",
  "SizeCategory": 1,
  "PaidPrice": 2.0,
  "Action": 1,
  "Status": 1
}

"""